{"mcpServers": {"Context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "0a368012-9074-4960-8fd8-a08aff2667f2"]}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "docker", "args": ["run", "--rm", "-i", "mcp/sequentialthinking"]}, "filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C://Users//tom7s//Desktopp"]}, "memory": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-memory"]}, "github": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "think": {"command": "docker", "args": ["run", "--rm", "-i", "mcp-think-tool"]}, "playwright cdp": {"cwd": "string", "env": {}, "args": ["/path/to/mcp-playwright/dist/index.js"], "shell": false, "command": "node"}, "sequential-thinking": {"command": "C:\\Users\\<USER>\\mcp-sequential-thinking\\.venv\\Scripts\\mcp-sequential-thinking.exe"}, "playwright": {"command": "cmd", "args": ["-y", "@executeautomation/playwright-mcp-server"]}}}